[project]
name = "pokemon"
version = "0.1.0"
description = "Pokemon Game Plugin for NoneBot2"
readme = "README.md"
requires-python = ">=3.8, <4.0"
dependencies = [
    "nonebot-adapter-onebot>=2.4.3",
    "nonebot2[fastapi]>=2.2.1",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "pydantic>=2.0.0",
    "pillow>=10.0.0",
    "aiofiles>=23.0.0",
    "httpx>=0.25.0",
    "asyncio-mqtt>=0.16.0",
    "python-dotenv>=1.0.0",
    "pytest>=8.3.5",
    "pokebase>=1.3.0",
    "requests>=2.31.0",
    "tqdm>=4.66.0",
]

[tool.nonebot]
adapters = [
    { name = "OneBot V11", module_name = "nonebot.adapters.onebot.v11" }
]
plugins = []
plugin_dirs = ["src/plugins"]
builtin_plugins = []
