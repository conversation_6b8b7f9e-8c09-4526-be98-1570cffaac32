# # 宝可梦游戏插件

一个基于NoneBot2的完整宝可梦游戏插件，实现了局外养成和局内PVP对战功能，基于第九世代（朱紫）的游戏内容。

## 功能特性

### 🎮 核心功能
- **用户系统**: 注册、等级、经验值、金币管理
- **宝可梦系统**: 捕捉、训练、进化、队伍管理
- **对战系统**: 玩家间PVP对战，完整的战斗逻辑
- **道具系统**: 精灵球、药品、进化道具等
- **数据持久化**: 基于SQLAlchemy的完整数据库设计

### 🔥 游戏特色
- **真实的宝可梦数据**: 基于第九世代的种族值、技能、属性相克
- **个体值系统**: 每只宝可梦都有独特的个体值
- **异色宝可梦**: 1/4096的概率遇到异色宝可梦
- **完整的对战系统**: 支持技能、伤害计算、状态效果
- **经济系统**: 金币、商店、道具购买

## 安装与配置

### 1. 环境要求
- Python 3.8+
- NoneBot2
- OneBot V11 协议适配器

### 2. 安装依赖
```bash
# 安装依赖
pip install -r requirements.txt
# 或使用 uv
uv sync
```

### 3. 配置环境变量
编辑 `.env` 文件：
```env
# OneBot 配置
ONEBOT_ACCESS_TOKEN=your_access_token_here
ONEBOT_SECRET=your_secret_here

# 宝可梦游戏配置
POKEMON_DB_URL=sqlite:///pokemon_game.db
POKEMON_DEBUG=true
POKEMON_MAX_TEAM_SIZE=6
POKEMON_DAILY_CATCH_LIMIT=10
```

### 4. 启动机器人
```bash
python bot.py
```

## 游戏命令

### 📋 基础命令
- `/pokemon help` - 查看帮助
- `/pokemon register` - 注册游戏账号
- `/pokemon profile` - 查看个人信息

### 🎯 养成系统
- `/pokemon catch` - 捕捉野生宝可梦
- `/pokemon train <ID>` - 训练宝可梦提升等级
- `/pokemon evolve <ID>` - 进化宝可梦
- `/pokemon team` - 查看当前队伍
- `/pokemon box` - 查看宝可梦盒子
- `/pokemon add <ID>` - 将宝可梦加入队伍
- `/pokemon remove <ID>` - 将宝可梦移出队伍

### 🎒 道具系统
- `/pokemon items` - 查看道具背包
- `/pokemon shop` - 查看商店
- `/pokemon buy <道具名> [数量]` - 购买道具
- `/pokemon use <道具名> <宝可梦ID>` - 使用道具

### ⚔️ 对战系统
- `/pokemon battle <@用户>` - 发起PVP对战
- `/pokemon accept` - 接受对战邀请
- `/pokemon decline` - 拒绝对战邀请
- `/pokemon move <技能名>` - 在对战中使用技能
- `/pokemon status` - 查看对战状态

## 项目结构

```
src/plugins/pokemon_game/
├── __init__.py              # 插件入口
├── config.py               # 配置文件
├── models/                 # 数据模型
│   ├── __init__.py
│   ├── base.py            # 数据库基础配置
│   ├── user.py            # 用户相关模型
│   ├── pokemon.py         # 宝可梦相关模型
│   ├── battle.py          # 对战相关模型
│   └── item.py            # 道具相关模型
├── core/                  # 核心业务逻辑
│   ├── __init__.py
│   ├── database.py        # 数据库管理
│   ├── data_loader.py     # 基础数据加载
│   ├── user_service.py    # 用户服务
│   ├── pokemon_service.py # 宝可梦服务
│   ├── battle_service.py  # 对战服务
│   ├── item_service.py    # 道具服务
│   └── game_engine.py     # 游戏引擎
├── commands/              # 命令处理
│   ├── __init__.py
│   ├── basic_commands.py  # 基础命令
│   ├── pokemon_commands.py # 宝可梦命令
│   ├── battle_commands.py # 对战命令
│   └── item_commands.py   # 道具命令
└── utils/                 # 工具模块
    ├── __init__.py
    └── message_builder.py # 消息构建器
```

## 开发说明

### 数据库设计
- **用户表**: 存储用户基本信息、等级、金币、对战记录
- **宝可梦种族表**: 存储宝可梦的基础数据（种族值、类型、技能等）
- **用户宝可梦表**: 存储用户拥有的宝可梦实例（个体值、等级、技能等）
- **对战表**: 存储对战记录和状态
- **道具表**: 存储道具信息和效果

### 扩展功能
插件设计为模块化架构，可以轻松扩展：
- 添加新的宝可梦种族
- 实现新的道具效果
- 添加PVE对战模式
- 实现交易系统
- 添加公会系统

## 测试

运行测试：
```bash
python tests/test_pokemon_game.py
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License

## How to start

1. generate project using `nb create` .
2. create your plugin using `nb plugin create` .
3. writing your plugins under `pokemon/plugins` folder.
4. run your bot using `nb run --reload` .

## Documentation

See [Docs](https://nonebot.dev/)
